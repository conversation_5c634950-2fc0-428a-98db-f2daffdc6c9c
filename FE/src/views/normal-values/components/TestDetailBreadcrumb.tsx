import { useParams } from 'react-router';
import { observer } from 'mobx-react-lite';
import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

import { getTestDetail } from '@/graphql/normal-values';

/**
 * TestDetailBreadcrumb - A component that displays the test name in the breadcrumb
 * instead of the generic "Equations" text.
 */
const TestDetailBreadcrumb = observer(() => {
  const { testId } = useParams();
  const [testName, setTestName] = useState<string>('Equations');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { data: testData } = useQuery(getTestDetail, {
    variables: { testid: parseInt(testId || '0', 10) },
    skip: !testId
  });

  useEffect(() => {
    if (!testData) return;

    setIsLoading(true);

    if (testData?.pred_ref_tests?.[0]) {
      setTestName(testData.pred_ref_tests[0].description || 'Equations');
    }

    setIsLoading(false);
  }, [testData]);

  return isLoading ? 'Equations' : testName;
});

export default TestDetailBreadcrumb;
