import clsx from 'clsx';
import {useMemo, useState, useEffect} from 'react';
import {Input} from 'react-aria-components';
import {Button as RACButton} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {ColDef, iconSetMaterial, themeQuartz} from 'ag-grid-enterprise';
import {AgGridReact, CustomCellRendererProps} from 'ag-grid-react';
import {differenceInDays, format} from 'date-fns';
import {Calendar as CalendarIcon, ChevronDown, PlusIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
// import {useDialogState} from '@/components/modal-state-provider';
import {Button} from '@/components/ui/button';
import {getWaitingListData} from '@/graphql/bookings.ts';
import {getProceduresList} from '@/graphql/lists.ts';

type GroupingOption = 'urgency' | 'procedure' | 'patient';

function WaitingList() {
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  const [groupBy, setGroupBy] = useState<GroupingOption>('urgency');
  const {data} = useQuery(getWaitingListData);
  const {data: procedures} = useQuery(getProceduresList);

  // const [, setWaitingListOpen] = useDialogState('add-waiting-list-patient');
  // const [, setRejectRefferalOpen] = useDialogState('reject-referral')

  const calculateDaysWaiting = (referralDate: string) => {
    const referral = new Date(referralDate);
    const today = new Date();
    return differenceInDays(today, referral);
  };


  const getProcedures = (procedureIds: number[]) => {
    return procedures?.procedure?.map((p) => procedureIds.includes(p.id) && p.name)?.filter(Boolean)?.join(', ') || undefined;
  };

  // Custom cell renderer for group rows
  const GroupCellRenderer = (params: CustomCellRendererProps & {[key: string]: any}) => {
    const [isExpanded, setIsExpanded] = useState(params.node.expanded || false);

    useEffect(() => {
      setIsExpanded(params.node.expanded);
    }, [params.node]);

    if (!params.node.group) return null;

    const groupKey = params.node.key;
    const childCount = params.node.allChildrenCount;
    const groupLevel = params.node.level;

    const handleToggleExpansion = () => {
      const newExpandedState = !isExpanded;
      setIsExpanded(newExpandedState);
      params.node.setExpanded(newExpandedState);
    };

    // Render urgency badge for level 0 (urgency groups)
    const renderUrgencyBadge = (urgency: string) => {
      return (
        <div
          className={clsx('inline-flex items-center rounded-sm px-2 py-1 text-xs capitalize mr-2', {
            'bg-[#FEF5F4] text-[#FE0008]': urgency === 'urgent',
            'bg-[#FCF9EE] text-[#8D6F00]': urgency === 'soon',
            'bg-[#F7F7F7] text-[#787878]': urgency === 'standard',
          })}
        >
          {urgency}
        </div>
      );
    };

    return (
      <div
        className="flex h-9.5 items-center font-normal cursor-pointer"
        onClick={handleToggleExpansion}
        style={{ paddingLeft: `${groupLevel * 20}px` }}
      >
        <div
          className={clsx(
            'mr-1.5 transform cursor-pointer text-neutral-500 transition-transform duration-200',
            isExpanded ? 'rotate-90' : 'rotate-0'
          )}
        >
          <ChevronDown className="h-4 w-4 text-neutral-800" />
        </div>
        <div className="flex items-center text-xs text-neutral-800">
          {groupLevel === 0 && renderUrgencyBadge(groupKey)}
          <div className={clsx('font-semibold', groupLevel === 0 ? '' : 'ml-0')}>
            {groupLevel === 0 ? '' : groupKey}
          </div>
          <div className="ml-2 text-neutral-600">({childCount})</div>
        </div>
      </div>
    );
  };

  const waitingListRecords = useMemo(() => {
    return data?.waiting_list?.map((item) => ({
      ...item,
      name: item.pas_pt.pas_pt_names[0].firstname + ' ' + item.pas_pt.pas_pt_names[0].surname,
      dob: item.pas_pt.dob,
      procedureNames: getProcedures(item.procedure_ids || []) || 'No procedures',
      daysWaiting: calculateDaysWaiting((item.referral_date as string) ?? item.created_at),
    }));
  }, [data?.waiting_list, procedures]);

  // Filter data by search (only by name)
  const filteredData = useMemo(() => {
    if (!waitingListRecords) return [];

    return waitingListRecords.filter((item) => {
      const search = debouncedSearch.toLowerCase();
      return (
        item.name.toLowerCase().includes(search) ||
        item?.urgency?.toLowerCase().includes(search) ||
        item.procedureNames?.toLowerCase().includes(search)
      );
    });
  }, [waitingListRecords, debouncedSearch]);

  // Column definitions for AG Grid
  const columnDefs: ColDef[] = [
    {
      field: 'urgency',
      headerName: 'Urgency',
      rowGroup: true,
      hide: true,
      valueGetter: (params) => {
        if (!params.data) return params.node?.key || '';
        return params.data.urgency || 'standard';
      },
      comparator: (valueA: string, valueB: string) => {
        const urgencyOrder = { urgent: 0, soon: 1, standard: 2 };
        return urgencyOrder[valueA as keyof typeof urgencyOrder] - urgencyOrder[valueB as keyof typeof urgencyOrder];
      },
    },
    {
      field: 'procedureNames',
      headerName: 'Procedure',
      rowGroup: true,
      hide: true,
      valueGetter: (params) => {
        if (!params.data) return params.node?.key || '';
        return params.data.procedureNames || 'No procedures';
      },
    },
    {
      field: 'name',
      headerName: 'Patient Name',
      rowGroup: true,
      hide: true,
      valueGetter: (params) => {
        if (!params.data) return params.node?.key || '';
        return params.data.name;
      },
    },
    {
      field: 'daysWaiting',
      headerName: 'Days Waiting',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.daysWaiting;
      },
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <div className="flex h-9.5 items-center text-right text-sm pr-9">
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'referral_date',
      headerName: 'Referral Date',
      valueGetter: (params) => {
        if (!params.data) return '';
        return format(new Date(params.data.referral_date as string), 'dd/MM/yyyy');
      },
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <div className="flex h-9.5 items-center text-sm">
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'dob',
      headerName: 'DOB',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.dob;
      },
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <div className="flex h-9.5 items-center text-sm">
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'provisional_procedure_date',
      headerName: 'Prov. Procedure Date',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.study_request_item?.provisional_procedure_date
          ? format(
              new Date(params.data.study_request_item?.provisional_procedure_date as string),
              'dd MMM yyyy'
            )
          : (params.data?.provisional_procedure_date as string) || '';
      },
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <div className="flex h-9.5 items-center text-sm">
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'notes',
      headerName: 'Notes',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.notes || '';
      },
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <div className="flex h-9.5 items-center text-sm">
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'actions',
      headerName: '',
      valueGetter: () => '',
      cellRenderer: (params: any) => {
        if (params.node.group) return null;
        return (
          <RACButton className="text-brand-500 hover:text-brand-400 flex cursor-pointer items-center gap-1 text-sm font-medium">
            <CalendarIcon className="h-4 w-4" />
            Schedule
          </RACButton>
        );
      },
      sortable: false,
      filter: false,
    },
  ];

  // AG Grid theme configuration
  const myTheme = themeQuartz.withPart(iconSetMaterial).withParams({
    fontFamily: 'Figtree, sans-serif',
    fontSize: 'var(--text-xs)',
    selectedRowBackgroundColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    rowHoverColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    backgroundColor: 'var(--color-neutral-100)',
    columnBorder: false,
    headerFontSize: 'var(--text-xs)',
    headerFontWeight: 600,
    headerTextColor: 'var(--color-neutral-800)',
    headerHeight: 48,
    iconSize: 13,
    rowHeight: 38,
    headerBackgroundColor: 'var(--color-white)',
    borderColor: 'var(--color-neutral-200)',
    textColor: 'var(--color-neutral-700)',
    rangeSelectionBorderColor: 'var(--color-brand2-500)',
    rangeHeaderHighlightColor: 'var(--color-brand2-500)',
    accentColor: 'var(--color-brand2-400)',
    rowBorder: true,
    selectCellBorder: true,
    wrapperBorder: true,
    borderRadius: 0,
    cellEditingShadow: false,
    selectCellBackgroundColor: 'var(--color-white)',
    cellHorizontalPadding: 12,
  });

  const defaultColDef = {
    resizable: true,
    suppressMovable: false,
    suppressHeaderFilterButton: true,
    suppressHeaderMenuButton: true,
    sortable: true,
    flex: 1,
  };

  const autoGroupColumnDef = {
    headerName: 'Waiting List',
    cellRenderer: GroupCellRenderer,
    cellRendererParams: {
      suppressCount: true,
    },
    minWidth: 300,
  };

  return (
    <div className="flex h-full flex-col gap-5">
      <div className="flex w-full items-center justify-between">
        <div className="relative w-90">
          <Input
            placeholder="Search patients, procedures or urgency..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <Button
          className="react-aria-Button ml-auto h-8 rounded-sm"
          onPress={() => {}}
        >
          <PlusIcon className="h-4 w-4" />
          Add referral
        </Button>
      </div>
      <div className="h-full w-full">
        <AgGridReact
          className="ag-waiting-list"
          rowData={filteredData}
          loading={!data}
          columnDefs={columnDefs}
          getRowId={(params) => String(params.data.id)}
          autoGroupColumnDef={autoGroupColumnDef}
          groupDisplayType="singleColumn"
          groupDefaultExpanded={0}
          defaultColDef={defaultColDef}
          suppressAggFuncInHeader
          animateRows
          pagination={false}
          theme={myTheme}
          domLayout="normal"
          overlayNoRowsTemplate={`<div class="text-center p-8">
            <div class="text-lg font-semibold text-neutral-900 mb-2">No waiting list entries found</div>
            <div class="text-sm text-neutral-600">Try refining your search or add a new referral.</div>
          </div>`}
        />
      </div>
    </div>
  );
}

export default WaitingList;
