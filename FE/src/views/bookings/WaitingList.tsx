import clsx from 'clsx';
import {Cell, Column, Input, Row, Table, TableHeader} from 'react-aria-components';
import {Button as RACButton} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {differenceInDays, format} from 'date-fns';
import {Calendar as CalendarIcon, PlusIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {LoadableTableBody} from '@/components/Table.tsx';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
// import {useDialogState} from '@/components/modal-state-provider';
import {Button} from '@/components/ui/button';
import {getWaitingListData} from '@/graphql/bookings.ts';
import {getProceduresList} from '@/graphql/lists.ts';

function WaitingList() {
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  const {data} = useQuery(getWaitingListData);
  const {data: procedures} = useQuery(getProceduresList);

  // const [, setWaitingListOpen] = useDialogState('add-waiting-list-patient');
  // const [, setRejectRefferalOpen] = useDialogState('reject-referral')

  const calculateDaysWaiting = (referralDate: string) => {
    const referral = new Date(referralDate);
    const today = new Date();
    return differenceInDays(today, referral);
  };


  const getProcedures = (procedureIds: number[]) => {
    return procedures?.procedure?.map((p) => procedureIds.includes(p.id) && p.name)?.filter(Boolean)?.join(', ') || undefined;
  };

  const waitingListRecords = data?.waiting_list?.map((item) => ({
    ...item,
    name: item.pas_pt.pas_pt_names[0].firstname + ' ' + item.pas_pt.pas_pt_names[0].surname,
    dob: item.pas_pt.dob,
  }));

  // Filter data by search (only by name)
  const filteredData = waitingListRecords?.filter((item) => {
    const search = debouncedSearch.toLowerCase();
    return item.name.toLowerCase().includes(search) || item?.urgency?.toLowerCase().includes(search) || getProcedures(item.procedure_ids || [])?.toLowerCase().includes(search);
  });

  return (
    <div className="flex h-full flex-col gap-5">
      <div className="flex w-full items-center justify-between">
        <div className="relative w-90">
          <Input
            placeholder="Search patients, procedures or urgency..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <Button
          className="react-aria-Button ml-auto h-8 rounded-sm"
          onPress={() => {}}
        >
          <PlusIcon className="h-4 w-4" />
          Add referral
        </Button>
      </div>
      <Table
        aria-label="Contacts List"
        className="react-aria-Table has-[.table-empty]:grow"
      >
        <TableHeader>
          <Column>Urgency</Column>
          <Column>Days Waiting</Column>
          <Column isRowHeader>Referral date</Column>
          <Column>Procedure</Column>
          <Column>Name</Column>
          <Column>DOB</Column>
          <Column>Prov. procedure date</Column>
          <Column>Notes</Column>
          <Column></Column>
        </TableHeader>

        <LoadableTableBody
          emptyTitle="No contacts found"
          emptyDescription="Try refining your search or add a new contact"
          items={filteredData}
          isLoading={false}
          columnCount={10}
        >
          {(referral) => {
            const daysWaiting = calculateDaysWaiting(
              (referral.referral_date as string) ?? referral.created_at
            );

            const procedures = getProcedures(referral.procedure_ids || []);
            return (
              <Row
                key={referral.id}
                // onAction={() => {
                //   setSelectedDoctorId(referral.id as unknown as string);
                //   setEditContact(true);
                // }}
              >
                <Cell>
                  <div
                    className={clsx('inline-flex items-center rounded-sm px-2 py-1 text-xs capitalize', {
                      'bg-[#FEF5F4] text-[#FE0008]': referral.urgency === 'urgent',
                      'bg-[#FCF9EE] text-[#8D6F00]': referral.urgency === 'soon',
                      'bg-[#F7F7F7] text-[#787878]': referral.urgency === 'standard',
                    })}
                  >
                    {referral.urgency}
                  </div>
                </Cell>
                <Cell className="react-aria-Cell text-right text-sm pr-9">{daysWaiting}</Cell>
                <Cell className="react-aria-Cell text-sm">
                  {format(new Date(referral.referral_date as string), 'dd/MM/yyyy')}
                </Cell>
                <Cell>
                  {procedures}
                </Cell>
                <Cell>{referral.name}</Cell>
                <Cell>{referral.dob}</Cell>
                <Cell>
                  {referral.study_request_item?.provisional_procedure_date
                    ? format(
                        new Date(referral.study_request_item?.provisional_procedure_date as string),
                        'dd MMM yyyy'
                      )
                    : (referral?.provisional_procedure_date as string)}
                </Cell>
                <Cell>{referral.notes}</Cell>
                <Cell>
                  <RACButton className="text-brand-500 hover:text-brand-400 flex cursor-pointer items-center gap-1 text-sm font-medium">
                    <CalendarIcon className="h-4 w-4" />
                    Schedule
                  </RACButton>
                </Cell>
              </Row>
            );
          }}
        </LoadableTableBody>
      </Table>
    </div>
  );
}

export default WaitingList;
